<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Meta tags for character encoding and responsive design -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Page title for Cart page -->
    <title>Shopping Cart - QuickMark Spaza Shop</title>
    
    <!-- Link to external CSS stylesheet -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- Simple cart-specific styles -->
    <style>
        .cart-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 20px;
        }
        
        .cart-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .item-info {
            flex: 1;
        }
        
        .item-name {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .item-price {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 0 20px;
        }
        
        .qty-btn {
            background: #3498db;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .qty-btn:hover {
            background: #2980b9;
        }
        
        .quantity {
            font-weight: bold;
            min-width: 20px;
            text-align: center;
        }
        
        .remove-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
        }
        
        .remove-btn:hover {
            background: #c0392b;
        }
        
        .cart-total {
            background: #f8f9fa;
            border: 2px solid #3498db;
            border-radius: 5px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }
        
        .total-amount {
            font-size: 1.5rem;
            font-weight: bold;
            color: #e74c3c;
        }
        
        .empty-cart {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        
        .add-products {
            background: #27ae60;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        .add-products:hover {
            background: #229954;
        }
        
        .checkout-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1.1rem;
            margin-top: 10px;
        }
        
        .checkout-btn:hover {
            background: #c0392b;
        }
    </style>
</head>
<body>
    <!-- Header section with navigation -->
    <header>
        <div class="container">
            <!-- Shop logo and name -->
            <div class="logo">
                <img src="images/logo.svg" alt="QuickMark Spaza Shop Logo" class="logo-image">
            </div>
            
            <!-- Navigation menu -->
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="contact.html">Contact</a></li>
                    <li><a href="cart.html" class="active">Cart</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main content area for Cart page -->
    <main>
        <!-- Page header section -->
        <section class="page-header">
            <div class="container">
                <h2>Shopping Cart</h2>
                <p>Review your selected items</p>
            </div>
        </section>

        <!-- Cart content -->
        <div class="cart-container">
            <!-- Quick add products section -->
            <div style="background: #e8f4fd; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                <h3>Quick Add Products:</h3>
                <button class="add-products" onclick="addToCart('Maize Meal', 25.99)">Add Maize Meal - R25.99</button>
                <button class="add-products" onclick="addToCart('Fresh Bread', 12.50)">Add Fresh Bread - R12.50</button>
                <button class="add-products" onclick="addToCart('Rice', 28.99)">Add Rice - R28.99</button>
                <button class="add-products" onclick="addToCart('Sugar', 22.50)">Add Sugar - R22.50</button>
                <button class="add-products" onclick="addToCart('Coca Cola', 15.00)">Add Coca Cola - R15.00</button>
                <button class="add-products" onclick="addToCart('Water', 8.50)">Add Water - R8.50</button>
            </div>

            <!-- Cart items container -->
            <div id="cartItems">
                <!-- Cart items will be displayed here by JavaScript -->
            </div>

            <!-- Cart total section -->
            <div id="cartTotal" class="cart-total" style="display: none;">
                <h3>Cart Summary</h3>
                <p>Total Items: <span id="totalItems">0</span></p>
                <p class="total-amount">Total Amount: R<span id="totalAmount">0.00</span></p>
                <button class="checkout-btn" onclick="checkout()">Proceed to Checkout</button>
                <button class="remove-btn" onclick="clearCart()" style="margin-left: 10px;">Clear Cart</button>
            </div>

            <!-- Empty cart message -->
            <div id="emptyCart" class="empty-cart">
                <h3>Your cart is empty</h3>
                <p>Add some products to get started!</p>
                <a href="products.html" style="color: #3498db; text-decoration: none;">← Browse Products</a>
            </div>
        </div>
    </main>

    <!-- Footer section -->
    <footer>
        <div class="container">
            <p>&copy; 2024 QuickMark Spaza Shop. Serving the community with pride.</p>
            <p>Contact us: 011-123-4567 | <EMAIL></p>
        </div>
    </footer>

    <!-- Link to JavaScript file -->
    <script src="script.js"></script>
    
    <!-- Simple cart JavaScript -->
    <script>
        // Simple cart array to store items
        let cart = [];

        /**
         * Function to add item to cart
         * Uses basic calculations and demonstrates loops
         */
        function addToCart(name, price) {
            // Check if item already exists in cart using for loop
            let existingItem = null;
            for (let i = 0; i < cart.length; i++) {
                if (cart[i].name === name) {
                    existingItem = cart[i];
                    break;
                }
            }

            // If item exists, increase quantity, otherwise add new item
            if (existingItem) {
                existingItem.quantity += 1;
                existingItem.total = existingItem.quantity * existingItem.price; // Simple calculation
            } else {
                cart.push({
                    name: name,
                    price: price,
                    quantity: 1,
                    total: price // Simple calculation: quantity * price
                });
            }

            // Update cart display
            displayCart();
            console.log(`Added ${name} to cart. Cart now has ${cart.length} different items.`);
        }

        /**
         * Function to remove item from cart
         */
        function removeFromCart(name) {
            // Use for loop to find and remove item
            for (let i = 0; i < cart.length; i++) {
                if (cart[i].name === name) {
                    cart.splice(i, 1); // Remove item from array
                    break;
                }
            }
            displayCart();
            console.log(`Removed ${name} from cart.`);
        }

        /**
         * Function to update quantity
         * Demonstrates basic calculations
         */
        function updateQuantity(name, change) {
            // Find item using for loop
            for (let i = 0; i < cart.length; i++) {
                if (cart[i].name === name) {
                    cart[i].quantity += change; // Simple addition/subtraction
                    
                    // Remove item if quantity becomes 0 or less
                    if (cart[i].quantity <= 0) {
                        cart.splice(i, 1);
                    } else {
                        // Recalculate total using simple multiplication
                        cart[i].total = cart[i].quantity * cart[i].price;
                    }
                    break;
                }
            }
            displayCart();
        }

        /**
         * Function to calculate cart totals
         * Uses for loop and basic calculations
         */
        function calculateTotals() {
            let totalItems = 0;
            let totalAmount = 0;

            // Use for loop to calculate totals
            for (let i = 0; i < cart.length; i++) {
                totalItems += cart[i].quantity; // Add up quantities
                totalAmount += cart[i].total;   // Add up totals
            }

            return {
                items: totalItems,
                amount: totalAmount.toFixed(2) // Round to 2 decimal places
            };
        }

        /**
         * Function to display cart contents
         * Demonstrates loops and DOM manipulation
         */
        function displayCart() {
            const cartItemsContainer = document.getElementById('cartItems');
            const cartTotalContainer = document.getElementById('cartTotal');
            const emptyCartContainer = document.getElementById('emptyCart');

            // Clear existing content
            cartItemsContainer.innerHTML = '';

            // Check if cart is empty
            if (cart.length === 0) {
                cartTotalContainer.style.display = 'none';
                emptyCartContainer.style.display = 'block';
                return;
            }

            // Hide empty cart message and show total
            emptyCartContainer.style.display = 'none';
            cartTotalContainer.style.display = 'block';

            // Use for loop to display each cart item
            for (let i = 0; i < cart.length; i++) {
                const item = cart[i];
                
                // Create cart item HTML
                const cartItemHTML = `
                    <div class="cart-item">
                        <div class="item-info">
                            <div class="item-name">${item.name}</div>
                            <div class="item-price">R${item.price.toFixed(2)} each</div>
                        </div>
                        <div class="quantity-controls">
                            <button class="qty-btn" onclick="updateQuantity('${item.name}', -1)">-</button>
                            <span class="quantity">${item.quantity}</span>
                            <button class="qty-btn" onclick="updateQuantity('${item.name}', 1)">+</button>
                        </div>
                        <div>
                            <div class="item-price">R${item.total.toFixed(2)}</div>
                            <button class="remove-btn" onclick="removeFromCart('${item.name}')">Remove</button>
                        </div>
                    </div>
                `;
                
                cartItemsContainer.innerHTML += cartItemHTML;
            }

            // Calculate and display totals
            const totals = calculateTotals();
            document.getElementById('totalItems').textContent = totals.items;
            document.getElementById('totalAmount').textContent = totals.amount;
        }

        /**
         * Function to clear entire cart
         */
        function clearCart() {
            cart = []; // Reset cart array
            displayCart();
            alert('Cart cleared!');
        }

        /**
         * Simple checkout function
         */
        function checkout() {
            if (cart.length === 0) {
                alert('Your cart is empty!');
                return;
            }

            const totals = calculateTotals();
            let message = `Checkout Summary:\n\n`;
            
            // Use for loop to create checkout summary
            for (let i = 0; i < cart.length; i++) {
                const item = cart[i];
                message += `${item.name} x${item.quantity} = R${item.total.toFixed(2)}\n`;
            }
            
            message += `\nTotal Items: ${totals.items}`;
            message += `\nTotal Amount: R${totals.amount}`;
            message += `\n\nThank you for shopping at QuickMark Spaza!`;
            
            alert(message);
            
            // Clear cart after checkout
            cart = [];
            displayCart();
        }

        // Initialize cart display when page loads
        document.addEventListener('DOMContentLoaded', function() {
            displayCart();
            console.log('Cart page loaded successfully');
        });
    </script>
</body>
</html>
