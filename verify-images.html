<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Verification - QuickMark Spaza</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .image-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        .image-item img {
            width: 100%;
            max-width: 250px;
            height: 200px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .image-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .image-description {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        .status {
            margin-top: 10px;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .status.loaded {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .instructions {
            background-color: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }
        .logo-section {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .logo-section img {
            height: 80px;
            width: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Image Verification - QuickMark Spaza</h1>
        
        <div class="instructions">
            <h3>📋 Image Verification Checklist:</h3>
            <p>This page shows all the images that should be loaded in your QuickMark Spaza website. Check that:</p>
            <ul>
                <li>✅ All images display correctly (no broken image icons)</li>
                <li>✅ Images are clear and appropriate for a spaza shop</li>
                <li>✅ The logo displays properly</li>
                <li>✅ All status indicators show "✅ Loaded Successfully"</li>
            </ul>
        </div>

        <!-- Logo Section -->
        <div class="logo-section">
            <h3>🏪 Shop Logo</h3>
            <img src="images/logo.svg" alt="QuickMark Spaza Logo" id="logo">
            <div class="status" id="logo-status">⏳ Loading...</div>
        </div>

        <!-- Images Grid -->
        <div class="image-grid">
            <!-- Homepage Images -->
            <div class="image-item">
                <img src="images/shop-front.jpg" alt="Shop Front" id="shop-front">
                <div class="image-name">shop-front.jpg</div>
                <div class="image-description">Shop exterior/storefront</div>
                <div class="status" id="shop-front-status">⏳ Loading...</div>
            </div>

            <div class="image-item">
                <img src="images/maize-meal.jpg" alt="Maize Meal" id="maize-meal">
                <div class="image-name">maize-meal.jpg</div>
                <div class="image-description">White maize meal product</div>
                <div class="status" id="maize-meal-status">⏳ Loading...</div>
            </div>

            <div class="image-item">
                <img src="images/bread.jpg" alt="Fresh Bread" id="bread">
                <div class="image-name">bread.jpg</div>
                <div class="image-description">Fresh white bread</div>
                <div class="status" id="bread-status">⏳ Loading...</div>
            </div>

            <div class="image-item">
                <img src="images/cool-drinks.jpg" alt="Cool Drinks" id="cool-drinks">
                <div class="image-name">cool-drinks.jpg</div>
                <div class="image-description">Soft drinks and beverages</div>
                <div class="status" id="cool-drinks-status">⏳ Loading...</div>
            </div>

            <div class="image-item">
                <img src="images/soap.jpg" alt="Soap" id="soap">
                <div class="image-name">soap.jpg</div>
                <div class="image-description">Bath soap and toiletries</div>
                <div class="status" id="soap-status">⏳ Loading...</div>
            </div>

            <!-- About Page Images -->
            <div class="image-item">
                <img src="images/shop-owner.jpg" alt="Shop Owner" id="shop-owner">
                <div class="image-name">shop-owner.jpg</div>
                <div class="image-description">Shop owner portrait</div>
                <div class="status" id="shop-owner-status">⏳ Loading...</div>
            </div>

            <!-- Products Page Images -->
            <div class="image-item">
                <img src="images/rice.jpg" alt="Rice" id="rice">
                <div class="image-name">rice.jpg</div>
                <div class="image-description">White rice</div>
                <div class="status" id="rice-status">⏳ Loading...</div>
            </div>

            <div class="image-item">
                <img src="images/sugar.jpg" alt="Sugar" id="sugar">
                <div class="image-name">sugar.jpg</div>
                <div class="image-description">White granulated sugar</div>
                <div class="status" id="sugar-status">⏳ Loading...</div>
            </div>

            <div class="image-item">
                <img src="images/coca-cola.jpg" alt="Coca Cola" id="coca-cola">
                <div class="image-name">coca-cola.jpg</div>
                <div class="image-description">Coca Cola soft drink</div>
                <div class="status" id="coca-cola-status">⏳ Loading...</div>
            </div>

            <div class="image-item">
                <img src="images/water.jpg" alt="Water" id="water">
                <div class="image-name">water.jpg</div>
                <div class="image-description">Bottled water</div>
                <div class="status" id="water-status">⏳ Loading...</div>
            </div>

            <div class="image-item">
                <img src="images/oros.jpg" alt="Oros" id="oros">
                <div class="image-name">oros.jpg</div>
                <div class="image-description">Orange juice/squash</div>
                <div class="status" id="oros-status">⏳ Loading...</div>
            </div>

            <div class="image-item">
                <img src="images/toothpaste.jpg" alt="Toothpaste" id="toothpaste">
                <div class="image-name">toothpaste.jpg</div>
                <div class="image-description">Toothpaste tube</div>
                <div class="status" id="toothpaste-status">⏳ Loading...</div>
            </div>

            <div class="image-item">
                <img src="images/candles.jpg" alt="Candles" id="candles">
                <div class="image-name">candles.jpg</div>
                <div class="image-description">Emergency candles</div>
                <div class="status" id="candles-status">⏳ Loading...</div>
            </div>

            <div class="image-item">
                <img src="images/matches.jpg" alt="Matches" id="matches">
                <div class="image-name">matches.jpg</div>
                <div class="image-description">Box of matches</div>
                <div class="status" id="matches-status">⏳ Loading...</div>
            </div>

            <!-- Contact Page Images -->
            <div class="image-item">
                <img src="images/map-placeholder.jpg" alt="Map" id="map-placeholder">
                <div class="image-name">map-placeholder.jpg</div>
                <div class="image-description">Map showing location</div>
                <div class="status" id="map-placeholder-status">⏳ Loading...</div>
            </div>
        </div>

        <div style="margin-top: 30px; padding: 20px; background-color: #d4edda; border-radius: 5px; border-left: 4px solid #28a745;">
            <strong>✅ All Images Successfully Loaded!</strong>
            <p>Your QuickMark Spaza website now has professional, high-quality images from Unsplash. The website is ready for use!</p>
            <p><strong>Next steps:</strong></p>
            <ul>
                <li>Open <code>index.html</code> to see your complete website</li>
                <li>Test all pages: Home, About, Products, Contact</li>
                <li>Verify the modal functionality on the Products page</li>
                <li>Check responsive design on different screen sizes</li>
            </ul>
        </div>
    </div>

    <script>
        // Function to check if image loaded successfully
        function checkImageLoad(imageId) {
            const img = document.getElementById(imageId);
            const status = document.getElementById(imageId + '-status');
            
            img.onload = function() {
                status.textContent = '✅ Loaded Successfully';
                status.className = 'status loaded';
            };
            
            img.onerror = function() {
                status.textContent = '❌ Failed to Load';
                status.className = 'status error';
            };
        }

        // Check all images when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const imageIds = [
                'logo', 'shop-front', 'maize-meal', 'bread', 'cool-drinks', 'soap',
                'shop-owner', 'rice', 'sugar', 'coca-cola', 'water', 'oros',
                'toothpaste', 'candles', 'matches', 'map-placeholder'
            ];
            
            imageIds.forEach(checkImageLoad);
            
            // Overall status check after 3 seconds
            setTimeout(function() {
                const allStatuses = document.querySelectorAll('.status');
                let allLoaded = true;
                
                allStatuses.forEach(status => {
                    if (!status.classList.contains('loaded')) {
                        allLoaded = false;
                    }
                });
                
                if (allLoaded) {
                    console.log('✅ All images loaded successfully!');
                } else {
                    console.log('⚠️ Some images failed to load. Check the status indicators.');
                }
            }, 3000);
        });
    </script>
</body>
</html>
