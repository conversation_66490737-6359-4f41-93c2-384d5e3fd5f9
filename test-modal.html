<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Test - QuickMark Spaza</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            padding: 50px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .test-button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
        }
        .test-button:hover {
            background-color: #2980b9;
        }
        .instructions {
            background-color: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Modal Functionality Test</h1>
        
        <div class="instructions">
            <h3>Test Instructions:</h3>
            <ol>
                <li>Click any of the test buttons below to open a modal</li>
                <li>Try closing the modal using:
                    <ul>
                        <li>The "×" close button in the top-right corner</li>
                        <li>The "Close" button at the bottom</li>
                        <li>Clicking outside the modal (on the dark background)</li>
                        <li>Pressing the ESC key on your keyboard</li>
                    </ul>
                </li>
                <li>Check that the modal displays correctly and closes properly</li>
                <li>Verify that scrolling is disabled when modal is open</li>
            </ol>
        </div>

        <h2>Test Different Products:</h2>
        
        <button class="test-button" onclick="showProductDetails('Maize Meal (2.5kg)', 'R25.99', 'Premium white maize meal, perfect for making pap and other traditional dishes. This high-quality product is sourced from local suppliers and provides excellent nutrition for your family.')">
            Test Maize Meal Modal
        </button>
        
        <button class="test-button" onclick="showProductDetails('Fresh Bread', 'R12.50', 'Freshly baked white bread delivered daily from our local bakery partner. Soft, fluffy, and perfect for sandwiches, toast, or enjoying with your favorite spread.')">
            Test Bread Modal
        </button>
        
        <button class="test-button" onclick="showProductDetails('Coca Cola (500ml)', 'R15.00', 'Ice-cold Coca Cola in 500ml bottles, perfect for any occasion. Refreshing and satisfying, this classic soft drink is always a favorite with customers of all ages.')">
            Test Coca Cola Modal
        </button>
        
        <button class="test-button" onclick="showProductDetails('Bath Soap', 'R8.99', 'Gentle cleansing soap with moisturizing properties. This high-quality soap is perfect for daily use and leaves your skin feeling clean, soft, and refreshed.')">
            Test Soap Modal
        </button>

        <h2>Test Results:</h2>
        <div id="testResults" style="margin-top: 20px; padding: 20px; background-color: #f8f9fa; border-radius: 5px; text-align: left;">
            <p><strong>Expected Behavior:</strong></p>
            <ul>
                <li>✅ Modal should open smoothly with fade-in animation</li>
                <li>✅ Modal content should display product name, price, and description</li>
                <li>✅ Close button (×) should work</li>
                <li>✅ Close button at bottom should work</li>
                <li>✅ Clicking outside modal should close it</li>
                <li>✅ ESC key should close modal</li>
                <li>✅ Page scrolling should be disabled when modal is open</li>
                <li>✅ Modal should close smoothly with fade-out animation</li>
            </ul>
        </div>

        <div style="margin-top: 30px; padding: 20px; background-color: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
            <strong>🔧 Troubleshooting:</strong>
            <p>If the modal doesn't work properly:</p>
            <ul>
                <li>Check browser console for JavaScript errors (F12 → Console)</li>
                <li>Ensure all files (HTML, CSS, JS) are in the same folder</li>
                <li>Try refreshing the page (Ctrl+F5 or Cmd+Shift+R)</li>
                <li>Test in a different browser (Chrome, Firefox, Safari, Edge)</li>
            </ul>
        </div>
    </div>

    <!-- Modal HTML (same as in products.html) -->
    <div id="productModal" class="modal hidden">
        <div class="modal-content">
            <span class="close-btn" onclick="closeProductModal()">&times;</span>
            <h3 id="modalTitle"></h3>
            <p id="modalPrice" style="font-size: 1.4rem; font-weight: bold; color: #e74c3c;"></p>
            <p id="modalDescription"></p>
            <button onclick="closeProductModal()">Close</button>
        </div>
    </div>

    <script src="script.js"></script>
    
    <script>
        // Additional test logging
        console.log('Modal test page loaded');
        
        // Override console.log to also display in page
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            
            // Add to test results if it's modal-related
            const message = args.join(' ');
            if (message.includes('modal') || message.includes('Modal')) {
                const testResults = document.getElementById('testResults');
                const logEntry = document.createElement('p');
                logEntry.style.color = '#27ae60';
                logEntry.style.fontSize = '0.9rem';
                logEntry.textContent = `✓ ${message}`;
                testResults.appendChild(logEntry);
            }
        };
    </script>
</body>
</html>
